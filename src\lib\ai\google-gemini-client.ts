import { GoogleGenAI } from '@google/genai';
import { RateLimiter } from './rate-limiter';
import { findRelevantExample, customizeQuery, extractParameters } from './sql-examples';
import { generateFallbackSQL, validateBasicSQL, cleanupQuery } from './fallback-sql-generator';

export class GoogleGeminiClient {
  private client: GoogleGenAI;
  private rateLimiter: RateLimiter;
  private static instance: GoogleGeminiClient;

  private constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY غير موجود في متغيرات البيئة');
    }

    this.client = new GoogleGenAI({
      apiKey: apiKey,
    });
    this.rateLimiter = RateLimiter.getInstance();
  }

  static getInstance(): GoogleGeminiClient {
    if (!GoogleGeminiClient.instance) {
      GoogleGeminiClient.instance = new GoogleGeminiClient();
    }
    return GoogleGeminiClient.instance;
  }

  // توليد وصف للجدول باستخدام Gemini مع إعادة المحاولة
  async generateTableDescription(
    tableName: string,
    columns: Array<{name: string, type: string, nullable: boolean}>,
    foreignKeys: Array<{columnName: string, referencedTable: string}>,
    sampleData?: unknown[]
  ): Promise<{
    description: string;
    purpose: string;
    domain: string;
    businessContext: string;
    keyFields: string[];
    relatedTables: string[];
  }> {
    const prompt = this.buildTableAnalysisPrompt(tableName, columns, foreignKeys, sampleData);

    // إعادة المحاولة مع تأخير متزايد
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        // انتظار قبل إرسال الطلب
        await this.rateLimiter.waitForNextRequest();
        const response = await this.client.models.generateContent({
          model: 'gemini-1.5-flash', // استخدام نموذج أكثر استقراراً
          contents: [
            {
              role: 'user',
              parts: [
                {
                  text: `أنت خبير في تحليل قواعد البيانات. مهمتك هي تحليل بنية الجداول وتوليد أوصاف دقيقة ومفيدة.

يجب أن تقوم بتحليل الجدول وتحديد:
1. الوصف العام للجدول ووظيفته
2. الغرض من الجدول في النظام
3. المجال أو القطاع الذي يخدمه
4. السياق التجاري أو الوظيفي
5. الحقول الرئيسية المهمة
6. الجداول المرتبطة

يجب أن تكون الإجابة بصيغة JSON صحيحة باللغة العربية.

${prompt}`
                }
              ]
            }
          ],
          config: {
            temperature: 0.3,
            maxOutputTokens: 1000
          }
        });

        const content = response.text;
        if (!content) {
          throw new Error('لم يتم الحصول على رد من Gemini');
        }

        // محاولة تحليل JSON
        try {
          return JSON.parse(content);
        } catch {
          // إذا فشل التحليل، نحاول استخراج JSON من النص
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
          }
          throw new Error('فشل في تحليل رد Gemini كـ JSON');
        }

      } catch (error: any) {
        console.error(`المحاولة ${attempt} فشلت:`, error);

        // التحقق من نوع الخطأ
        if (error?.message?.includes('429') || error?.message?.includes('quota') || error?.message?.includes('rate')) {
          this.rateLimiter.resetOnError();
          const waitTime = attempt * 20000; // 20 ثانية، 40 ثانية، 60 ثانية
          console.log(`تم تجاوز الحد المسموح. انتظار ${waitTime/1000} ثانية قبل المحاولة التالية...`);

          if (attempt < 3) {
            await new Promise(resolve => setTimeout(resolve, waitTime));
            continue;
          }
        }

        // إذا كانت المحاولة الأخيرة أو خطأ آخر
        if (attempt === 3) {
          throw new Error(`فشل في توليد وصف الجدول بعد 3 محاولات: ${error?.message || error}`);
        }
      }
    }

    // هذا السطر لن يتم الوصول إليه، لكنه مطلوب للـ TypeScript
    throw new Error('فشل غير متوقع في توليد وصف الجدول');
  }

  // بناء prompt لتحليل الجدول
  private buildTableAnalysisPrompt(
    tableName: string,
    columns: Array<{name: string, type: string, nullable: boolean}>,
    foreignKeys: Array<{columnName: string, referencedTable: string}>,
    sampleData?: unknown[]
  ): string {
    let prompt = `حلل الجدول التالي وقدم وصفاً شاملاً:

اسم الجدول: ${tableName}

الأعمدة:
${columns.map(col => 
  `- ${col.name} (${col.type}) ${col.nullable ? '- يمكن أن يكون فارغ' : '- مطلوب'}`
).join('\n')}`;

    if (foreignKeys.length > 0) {
      prompt += `\n\nالمفاتيح الخارجية:
${foreignKeys.map(fk => 
  `- ${fk.columnName} يشير إلى جدول ${fk.referencedTable}`
).join('\n')}`;
    }

    if (sampleData && sampleData.length > 0) {
      prompt += `\n\nعينة من البيانات:
${JSON.stringify(sampleData.slice(0, 3), null, 2)}`;
    }

    prompt += `\n\nيرجى تحليل هذا الجدول وإرجاع النتيجة بصيغة JSON التالية:
{
  "description": "وصف مفصل للجدول ووظيفته",
  "purpose": "الغرض الأساسي من الجدول",
  "domain": "المجال أو القطاع (مثل: التجارة الإلكترونية، إدارة الموارد البشرية، المحاسبة، إلخ)",
  "businessContext": "السياق التجاري أو الوظيفي للجدول",
  "keyFields": ["قائمة بأهم الحقول في الجدول"],
  "relatedTables": ["قائمة بالجداول المرتبطة المحتملة"]
}

تأكد من أن الإجابة بصيغة JSON صحيحة وباللغة العربية.`;

    return prompt;
  }

  // توليد embeddings للنص باستخدام Gemini
  async generateEmbeddings(text: string): Promise<number[]> {
    try {
      // Gemini لا يدعم embeddings مباشرة، لذا سنستخدم text-embedding-004 من Google AI
      // أو يمكننا استخدام نموذج آخر للحصول على تمثيل رقمي للنص
      // في هذا المثال، سنقوم بتحويل النص إلى hash ثم إلى array من الأرقام
      // هذا حل مؤقت - في الواقع يجب استخدام embedding model مخصص
      
      const hash = this.simpleHash(text);
      const embedding = this.hashToEmbedding(hash, 1536); // نفس حجم OpenAI embeddings
      
      return embedding;
    } catch (error) {
      console.error('خطأ في توليد embeddings:', error);
      throw new Error(`فشل في توليد embeddings: ${error}`);
    }
  }

  // دالة مساعدة لتحويل النص إلى hash
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  // دالة مساعدة لتحويل hash إلى embedding vector
  private hashToEmbedding(hash: number, size: number): number[] {
    const embedding = new Array(size);
    let seed = hash;
    
    for (let i = 0; i < size; i++) {
      // استخدام Linear Congruential Generator لتوليد أرقام pseudo-random
      seed = (seed * 1664525 + 1013904223) % Math.pow(2, 32);
      embedding[i] = (seed / Math.pow(2, 32)) * 2 - 1; // تطبيع بين -1 و 1
    }
    
    // تطبيع الـ vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / magnitude);
  }

  // توليد استعلام SQL بناءً على سؤال المستخدم مع إعادة المحاولة
  async generateSQLQuery(
    userQuestion: string,
    relevantTables: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>,
    databaseType: 'mysql' | 'mssql'
  ): Promise<{
    query: string;
    explanation: string;
    confidence: number;
  }> {
    // إعطاء الأولوية للذكاء الاصطناعي بدلاً من الأمثلة المحفوظة
    const prompt = this.buildSQLGenerationPrompt(userQuestion, relevantTables, databaseType);

    // إعادة المحاولة مع تأخير متزايد
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        // انتظار قبل إرسال الطلب
        await this.rateLimiter.waitForNextRequest();
        const response = await this.client.models.generateContent({
          model: 'gemini-1.5-flash', // استخدام نموذج أكثر استقراراً
          contents: [
            {
              role: 'user',
              parts: [
                {
                  text: `أنت خبير في كتابة استعلامات SQL متقدم وذكي. مهمتك تحليل سؤال المستخدم بعمق وتوليد استعلام SQL دقيق.

🧠 **منهجية التحليل الذكي:**
1. **فهم القصد:** حلل ما يريده المستخدم بالضبط
2. **تحديد الكيانات:** ما هي الجداول والأعمدة المطلوبة؟
3. **فهم العلاقات:** كيف ترتبط الجداول ببعضها؟
4. **تحديد المرشحات:** ما هي الشروط المطلوبة؟

⚠️ **قواعد أساسية:**
- للعملاء والمبيعات: customers → invoices → invoice_details → items
- stock_movements للمخزون الداخلي فقط (لا يحتوي customer_id)
- استخدم LIKE '%term%' للبحث النصي المرن
- استخدم أسماء الأعمدة الصحيحة من الجداول المتاحة

🎯 **أمثلة ذكية:**
- "مبيعات البرتقال" → WHERE item_name LIKE '%برتقال%'
- "أكثر العملاء" → GROUP BY customer + ORDER BY SUM DESC
- "مشتريات فاطمة" → WHERE customer_name LIKE '%فاطمة%'

📊 **تحليل السياق:**
- إذا ذُكر منتج محدد → أضف شرط على item_name
- إذا ذُكر عميل محدد → أضف شرط على customer_name
- إذا ذُكر تاريخ → أضف شرط على التاريخ المناسب

يجب أن تكون الإجابة بصيغة JSON صحيحة.

${prompt}`
                }
              ]
            }
          ],
          config: {
            temperature: 0.1,
            maxOutputTokens: 1500
          }
        });

        const content = response.text;
        if (!content) {
          throw new Error('لم يتم الحصول على رد من Gemini');
        }

        try {
          return JSON.parse(content);
        } catch {
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            return JSON.parse(jsonMatch[0]);
          }
          throw new Error('فشل في تحليل رد Gemini كـ JSON');
        }

      } catch (error: any) {
        console.error(`المحاولة ${attempt} فشلت في توليد SQL:`, error);

        // التحقق من نوع الخطأ
        if (error?.message?.includes('429') || error?.message?.includes('quota') || error?.message?.includes('rate')) {
          this.rateLimiter.resetOnError();
          const waitTime = attempt * 15000; // 15 ثانية، 30 ثانية، 45 ثانية
          console.log(`تم تجاوز الحد المسموح. انتظار ${waitTime/1000} ثانية قبل المحاولة التالية...`);

          if (attempt < 3) {
            await new Promise(resolve => setTimeout(resolve, waitTime));
            continue;
          }
        }

        // إذا كانت المحاولة الأخيرة، استخدم نظام fallback
        if (attempt === 3) {
          console.log('فشل في توليد SQL عبر API، محاولة استخدام نظام fallback...');

          // استخدام نظام fallback ذكي كحل أخير
          console.log('محاولة استخدام نظام fallback ذكي...');
          const fallbackResult = generateFallbackSQL(userQuestion);
          if (fallbackResult) {
            return {
              query: cleanupQuery(fallbackResult.query),
              explanation: `${fallbackResult.explanation}\n\n⚠️ تم استخدام نظام fallback بسبب مشكلة في API.`,
              confidence: fallbackResult.confidence
            };
          }

          throw new Error(`فشل في توليد استعلام SQL بعد 3 محاولات: ${error?.message || error}`);
        }
      }
    }

    // هذا السطر لن يتم الوصول إليه، لكنه مطلوب للـ TypeScript
    throw new Error('فشل غير متوقع في توليد استعلام SQL');
  }

  // بناء prompt لتوليد SQL
  private buildSQLGenerationPrompt(
    userQuestion: string,
    relevantTables: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>,
    databaseType: 'mysql' | 'mssql'
  ): string {
    const prompt = `🎯 **مهمة تحليل ذكي لسؤال SQL:**

**السؤال:** "${userQuestion}"
**نوع قاعدة البيانات:** ${databaseType}

📋 **الجداول والبيانات المتاحة:**
${relevantTables.map(table => `
**جدول: ${table.name}**
- الوصف: ${table.description}
- الأعمدة: ${table.columns.map(col => `${col.name} (${col.type})`).join(', ')}
- العلاقات: ${table.relationships.map(rel => `${rel.fromColumn} → ${rel.toTable}.${rel.toColumn}`).join(', ')}
`).join('\n')}

🧠 **تحليل السؤال:**
1. حلل السؤال لفهم القصد الحقيقي
2. حدد الكيانات المطلوبة (عملاء، منتجات، مبيعات، إلخ)
3. اختر الجداول المناسبة بناءً على الوصف والعلاقات
4. استخدم البحث المرن (LIKE) للنصوص
5. طبق المرشحات المناسبة

⚡ **قواعد ذكية:**
- للعملاء والمبيعات: customers → invoices → invoice_details → items
- للبحث عن منتج: WHERE item_name LIKE '%اسم_المنتج%'
- للبحث عن عميل: WHERE customer_name LIKE '%اسم_العميل%'
- stock_movements للمخزون الداخلي فقط

📤 **المطلوب - JSON:**
{
  "query": "استعلام SQL محسن وذكي",
  "explanation": "شرح واضح لكيفية حل السؤال",
  "confidence": 0.9
}`;

    return prompt;
  }
}
