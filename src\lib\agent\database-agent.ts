import { GoogleGeminiClient } from '../ai/google-gemini-client';
import { SchemaEnricher } from '../ai/schema-enricher';
import { SchemaManager } from '../database/schema-manager';
import {
  EnrichedDatabaseSchema,
  DatabaseConnection,
  TableDescription
} from '../database/types';

// نتيجة استعلام الوكيل
export interface AgentQueryResult {
  query: string;
  explanation: string;
  confidence: number;
  relevantTables: string[];
  executionTime?: number;
  results?: any[];
  error?: string;
}

// حالة الوكيل
export interface AgentState {
  isInitialized: boolean;
  hasSchema: boolean;
  schemaLastUpdated?: string;
  totalTables: number;
  databaseType?: 'mysql' | 'mssql';
}

export class DatabaseAgent {
  private geminiClient: GoogleGeminiClient;
  private schemaEnricher: SchemaEnricher;
  private schemaManager: SchemaManager;
  private enrichedSchema: EnrichedDatabaseSchema | null = null;
  private isInitialized = false;

  constructor() {
    this.geminiClient = GoogleGeminiClient.getInstance();
    this.schemaEnricher = new SchemaEnricher();
    this.schemaManager = SchemaManager.getInstance();
  }

  // تهيئة الوكيل مع قاعدة البيانات
  async initialize(connection?: DatabaseConnection): Promise<void> {
    try {
      console.log('بدء تهيئة الوكيل الذكي...');

      // حفظ معلومات الاتصال إذا تم توفيرها
      if (connection) {
        this.schemaManager.setCurrentConnection(connection);
      }

      // محاولة تحميل Schema المحسن الموجود
      this.enrichedSchema = await this.schemaManager.loadEnrichedSchema();

      if (!this.enrichedSchema && connection) {
        console.log('لم يتم العثور على Schema محسن، بدء استخراج جديد...');
        
        // استخراج Schema جديد
        const schema = await this.schemaManager.extractSchema(connection);
        
        // إثراء Schema بالأوصاف
        this.enrichedSchema = await this.schemaEnricher.enrichSchema(schema);
        
        // حفظ Schema المحسن
        await this.schemaManager.saveEnrichedSchema(this.enrichedSchema);
      }

      if (this.enrichedSchema) {
        this.isInitialized = true;
        console.log(`تم تهيئة الوكيل بنجاح مع ${this.enrichedSchema.tables.length} جدول`);

        // إنشاء الوكيل الذكي
        await this.initializeIntelligentAgent();
      } else {
        throw new Error('فشل في تحميل أو إنشاء Schema');
      }

    } catch (error) {
      console.error('خطأ في تهيئة الوكيل:', error);
      throw error;
    }
  }

  // إنشاء الوكيل الذكي
  private async initializeIntelligentAgent(): Promise<void> {
    if (!this.enrichedSchema) return;

    try {
      console.log('🚀 إنشاء الوكيل الذكي...');

      // تحضير معلومات الجداول للوكيل الذكي
      const tablesInfo = this.enrichedSchema.tables.map(table => ({
        name: table.name,
        description: this.enrichedSchema!.tableDescriptions.find(desc => desc.tableName === table.name)?.description || `جدول ${table.name}`,
        columns: table.columns.map(col => ({
          name: col.name,
          type: col.type
        })),
        relationships: table.foreignKeys.map(fk => ({
          toTable: fk.referencedTable,
          fromColumn: fk.columnName,
          toColumn: fk.referencedColumn
        }))
      }));

      // إنشاء الوكيل الذكي
      await this.geminiClient.initializeIntelligentAgent(tablesInfo);
      console.log('✅ تم إنشاء الوكيل الذكي بنجاح');

    } catch (error) {
      console.error('❌ فشل في إنشاء الوكيل الذكي:', error);
      // لا نرمي خطأ هنا لأن النظام يمكن أن يعمل بدون الوكيل الذكي
    }
  }

  // معالجة سؤال المستخدم
  async processQuery(userQuestion: string): Promise<AgentQueryResult> {
    if (!this.isInitialized || !this.enrichedSchema) {
      throw new Error('الوكيل غير مهيأ. يرجى تهيئة الوكيل أولاً.');
    }

    const startTime = Date.now();

    try {
      console.log(`معالجة السؤال: ${userQuestion}`);

      // البحث عن الجداول المناسبة
      const relevantTables = await this.schemaEnricher.findRelevantTables(
        userQuestion,
        this.enrichedSchema,
        5
      );

      if (relevantTables.length === 0) {
        return {
          query: '',
          explanation: 'لم يتم العثور على جداول مناسبة للإجابة على هذا السؤال',
          confidence: 0,
          relevantTables: [],
          executionTime: Date.now() - startTime,
          error: 'لا توجد جداول مناسبة'
        };
      }

      console.log(`تم العثور على ${relevantTables.length} جدول مناسب`);

      // تحضير معلومات الجداول للـ AI
      const tablesInfo = relevantTables.map(rt => {
        const table = this.enrichedSchema!.tables.find(t => t.name === rt.tableName);
        if (!table) return null;

        return {
          name: table.name,
          description: rt.description.description,
          columns: table.columns.map(col => ({
            name: col.name,
            type: col.type
          })),
          relationships: table.foreignKeys.map(fk => ({
            toTable: fk.referencedTable,
            fromColumn: fk.columnName,
            toColumn: fk.referencedColumn
          }))
        };
      }).filter(Boolean) as any[];

      // توليد استعلام SQL
      const sqlResult = await this.geminiClient.generateSQLQuery(
        userQuestion,
        tablesInfo,
        this.enrichedSchema.databaseType
      );

      console.log('تم توليد الاستعلام:', sqlResult.query);

      // تنفيذ الاستعلام
      let queryResults: any[] = [];
      let executionError: string | undefined;

      try {
        console.log('جاري تنفيذ الاستعلام...');
        const results = await this.schemaManager.executeQuery(sqlResult.query);
        queryResults = results.data || [];
        console.log(`تم تنفيذ الاستعلام بنجاح. عدد النتائج: ${queryResults.length}`);
        console.log('البيانات المسترجعة:', queryResults);
      } catch (error) {
        console.error('خطأ في تنفيذ الاستعلام:', error);
        executionError = error instanceof Error ? error.message : 'خطأ في تنفيذ الاستعلام';
      }

      const executionTime = Date.now() - startTime;

      return {
        query: sqlResult.query,
        explanation: sqlResult.explanation,
        confidence: sqlResult.confidence,
        relevantTables: relevantTables.map(rt => rt.tableName),
        executionTime,
        results: queryResults,
        error: executionError
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';

      console.error('خطأ في معالجة السؤال:', error);

      return {
        query: '',
        explanation: 'حدث خطأ أثناء معالجة السؤال',
        confidence: 0,
        relevantTables: [],
        executionTime,
        error: errorMessage
      };
    }
  }

  // تحديث Schema
  async updateSchema(connection: DatabaseConnection): Promise<void> {
    try {
      console.log('بدء تحديث Schema...');

      // استخراج Schema جديد
      const schema = await this.schemaManager.extractSchema(connection);
      
      // إثراء Schema بالأوصاف
      this.enrichedSchema = await this.schemaEnricher.enrichSchema(schema);
      
      // حفظ Schema المحسن
      await this.schemaManager.saveEnrichedSchema(this.enrichedSchema);

      console.log('تم تحديث Schema بنجاح');

    } catch (error) {
      console.error('خطأ في تحديث Schema:', error);
      throw error;
    }
  }

  // الحصول على حالة الوكيل
  getState(): AgentState {
    return {
      isInitialized: this.isInitialized,
      hasSchema: this.enrichedSchema !== null,
      schemaLastUpdated: this.enrichedSchema?.extractedAt,
      totalTables: this.enrichedSchema?.tables.length || 0,
      databaseType: this.enrichedSchema?.databaseType
    };
  }

  // الحصول على معلومات الجداول
  getTablesInfo(): Array<{
    name: string;
    description: string;
    domain: string;
    columnsCount: number;
    rowCount?: number;
  }> {
    if (!this.enrichedSchema) return [];

    return this.enrichedSchema.tableDescriptions.map(desc => {
      const table = this.enrichedSchema!.tables.find(t => t.name === desc.tableName);
      return {
        name: desc.tableName,
        description: desc.description,
        domain: desc.domain,
        columnsCount: table?.columns.length || 0,
        rowCount: table?.rowCount
      };
    });
  }

  // البحث في الجداول
  searchTables(searchTerm: string): Array<{
    name: string;
    description: string;
    relevance: number;
  }> {
    if (!this.enrichedSchema) return [];

    const searchLower = searchTerm.toLowerCase();
    
    return this.enrichedSchema.tableDescriptions
      .map(desc => {
        let relevance = 0;
        
        // البحث في اسم الجدول
        if (desc.tableName.toLowerCase().includes(searchLower)) {
          relevance += 10;
        }
        
        // البحث في الوصف
        if (desc.description.toLowerCase().includes(searchLower)) {
          relevance += 5;
        }
        
        // البحث في المجال
        if (desc.domain.toLowerCase().includes(searchLower)) {
          relevance += 3;
        }
        
        // البحث في الحقول الرئيسية
        if (desc.keyFields.some(field => field.toLowerCase().includes(searchLower))) {
          relevance += 2;
        }

        return {
          name: desc.tableName,
          description: desc.description,
          relevance
        };
      })
      .filter(item => item.relevance > 0)
      .sort((a, b) => b.relevance - a.relevance);
  }

  // الحصول على تفاصيل جدول محدد
  getTableDetails(tableName: string): {
    table: any;
    description: TableDescription;
  } | null {
    if (!this.enrichedSchema) return null;

    const table = this.enrichedSchema.tables.find(t => t.name === tableName);
    const description = this.enrichedSchema.tableDescriptions.find(d => d.tableName === tableName);

    if (!table || !description) return null;

    return { table, description };
  }

  // إعادة تعيين الوكيل
  reset(): void {
    this.enrichedSchema = null;
    this.isInitialized = false;
    console.log('تم إعادة تعيين الوكيل');
  }
}
