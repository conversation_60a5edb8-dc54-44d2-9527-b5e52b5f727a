"use client"

import React, { useState, useMemo } from 'react';
import { DataTable } from './data-table';
import { ChartContainer } from './chart-container';
import { Table, BarChart3, FileText, TrendingUp, Lightbulb, Target, Award } from 'lucide-react';
import { cn } from '@/lib/utils';
import { generateSmartSummary } from '@/lib/utils/data-analyzer';

interface ResultsDisplayProps {
  data: any[];
  query: string;
  explanation?: string;
  executionTime?: number;
  className?: string;
}

type ViewMode = 'table' | 'chart' | 'summary';

export function ResultsDisplay({
  data,
  query,
  explanation,
  executionTime,
  className = ""
}: ResultsDisplayProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('table');

  // تشخيص البيانات
  console.log('ResultsDisplay - البيانات المستلمة:', { data, query, explanation });
  const [chartConfig, setChartConfig] = useState<{
    xKey: string;
    yKey: string;
    chartType: 'bar' | 'line' | 'pie';
  } | null>(null);

  // تحليل البيانات لاستخراج معلومات مفيدة
  const dataAnalysis = useMemo(() => {
    if (!data || data.length === 0) return null;

    const columns = Object.keys(data[0]);
    const numericColumns = columns.filter(col => 
      data.every(row => typeof row[col] === 'number' || !isNaN(Number(row[col])))
    );
    const textColumns = columns.filter(col => !numericColumns.includes(col));

    // اقتراح إعدادات الرسم البياني
    const suggestedChart = {
      xKey: textColumns[0] || columns[0],
      yKey: numericColumns[0] || columns[1] || columns[0],
      chartType: 'bar' as const
    };

    // إحصائيات أساسية
    const stats = {
      totalRows: data.length,
      totalColumns: columns.length,
      numericColumns: numericColumns.length,
      textColumns: textColumns.length
    };

    return {
      columns,
      numericColumns,
      textColumns,
      suggestedChart,
      stats
    };
  }, [data]);

  // تهيئة إعدادات الرسم البياني
  React.useEffect(() => {
    if (dataAnalysis && !chartConfig) {
      setChartConfig(dataAnalysis.suggestedChart);
    }
  }, [dataAnalysis, chartConfig]);

  const generateSummary = () => {
    if (!data || !dataAnalysis) return null;

    const { stats, numericColumns } = dataAnalysis;
    
    // حساب إحصائيات للأعمدة الرقمية
    const numericStats = numericColumns.map(col => {
      const values = data.map(row => Number(row[col])).filter(val => !isNaN(val));
      const sum = values.reduce((a, b) => a + b, 0);
      const avg = sum / values.length;
      const min = Math.min(...values);
      const max = Math.max(...values);

      return {
        column: col,
        sum,
        average: avg,
        min,
        max,
        count: values.length
      };
    });

    return {
      stats,
      numericStats
    };
  };

  const summary = generateSummary();

  // توليد خلاصة ذكية للبيانات
  const smartSummary = useMemo(() => {
    return generateSmartSummary(data, query);
  }, [data, query]);

  if (!data || data.length === 0) {
    return (
      <div className={cn("bg-white rounded-lg shadow-md p-8 text-center", className)}>
        <div className="text-gray-500">
          <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">لا توجد نتائج</h3>
          <p>لم يتم إرجاع أي بيانات من الاستعلام</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with elegant title */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">نتائج التحليل</h3>
            <p className="text-sm text-gray-600">تم العثور على {data.length} نتيجة</p>
          </div>
        </div>
      </div>

      {/* View Mode Selector */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="flex items-center gap-2 mb-4">
          <button
            onClick={() => setViewMode('table')}
            className={cn(
              "flex items-center gap-2 px-4 py-2 rounded-lg transition-colors",
              viewMode === 'table'
                ? "bg-blue-600 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            <Table className="w-4 h-4" />
            جدول
          </button>

          {dataAnalysis && dataAnalysis.numericColumns.length > 0 && (
            <button
              onClick={() => setViewMode('chart')}
              className={cn(
                "flex items-center gap-2 px-4 py-2 rounded-lg transition-colors",
                viewMode === 'chart'
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              )}
            >
              <BarChart3 className="w-4 h-4" />
              رسم بياني
            </button>
          )}

          <button
            onClick={() => setViewMode('summary')}
            className={cn(
              "flex items-center gap-2 px-4 py-2 rounded-lg transition-colors",
              viewMode === 'summary'
                ? "bg-blue-600 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
          >
            <TrendingUp className="w-4 h-4" />
            ملخص
          </button>
        </div>

        {/* Chart Configuration */}
        {viewMode === 'chart' && dataAnalysis && chartConfig && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">إعدادات الرسم البياني:</h4>
            <div className="flex items-center gap-4">
              <div>
                <label className="block text-xs text-gray-600 mb-1">المحور السيني:</label>
                <select
                  value={chartConfig.xKey}
                  onChange={(e) => setChartConfig({...chartConfig, xKey: e.target.value})}
                  className="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  {dataAnalysis.columns.map(col => (
                    <option key={col} value={col}>{col}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">المحور الصادي:</label>
                <select
                  value={chartConfig.yKey}
                  onChange={(e) => setChartConfig({...chartConfig, yKey: e.target.value})}
                  className="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  {dataAnalysis.numericColumns.map(col => (
                    <option key={col} value={col}>{col}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      {viewMode === 'table' && (
        <DataTable 
          data={data} 
          title={`نتائج الاستعلام (${data.length} سجل)`}
        />
      )}

      {viewMode === 'chart' && chartConfig && dataAnalysis && (
        <ChartContainer
          data={data}
          title="الرسم البياني للنتائج"
          xKey={chartConfig.xKey}
          yKey={chartConfig.yKey}
          chartType={chartConfig.chartType}
        />
      )}

      {viewMode === 'summary' && (
        <div className="space-y-6">
          {/* Smart Summary Header */}
          <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">{smartSummary.title}</h3>
                <p className="text-indigo-100">{smartSummary.description}</p>
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          {smartSummary.keyMetrics.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {smartSummary.keyMetrics.map((metric, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6 border-r-4 border-blue-500">
                  <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
                  <div className="text-sm text-gray-600">{metric.label}</div>
                  {metric.change && (
                    <div className={cn(
                      "text-xs mt-1 flex items-center gap-1",
                      metric.trend === 'up' ? 'text-green-600' :
                      metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                    )}>
                      {metric.trend === 'up' && '↗️'}
                      {metric.trend === 'down' && '↘️'}
                      {metric.change}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Insights */}
          {smartSummary.insights.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Lightbulb className="w-5 h-5 text-yellow-500" />
                رؤى ذكية
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {smartSummary.insights.map((insight, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start gap-3">
                      <span className="text-2xl">{insight.icon}</span>
                      <div className="flex-1">
                        <h5 className={cn("font-semibold mb-1", insight.color)}>{insight.title}</h5>
                        <p className="text-sm text-gray-600 mb-2">{insight.description}</p>
                        {insight.value && (
                          <div className={cn("text-lg font-bold", insight.color)}>{insight.value}</div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recommendations */}
          {smartSummary.recommendations.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Target className="w-5 h-5 text-green-500" />
                التوصيات
              </h4>
              <div className="space-y-3">
                {smartSummary.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                    <Award className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-green-800">{recommendation}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
