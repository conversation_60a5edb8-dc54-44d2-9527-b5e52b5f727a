// وكيل ذكي حقيقي لتوليد SQL باستخدام LLM فقط

export interface DatabaseSchema {
  tables: TableSchema[];
}

export interface TableSchema {
  name: string;
  description: string;
  columns: ColumnSchema[];
  relationships: RelationshipSchema[];
}

export interface ColumnSchema {
  name: string;
  type: string;
  description: string;
  nullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
}

export interface RelationshipSchema {
  fromTable: string;
  fromColumn: string;
  toTable: string;
  toColumn: string;
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
}

export interface SQLResult {
  query: string;
  explanation: string;
  confidence: number;
  reasoning: string[];
}

/**
 * وكيل ذكي لتحليل الأسئلة وتوليد SQL
 * يعتمد على فهم عميق لبنية قاعدة البيانات والسياق اللغوي
 */
export class IntelligentSQLAgent {
  private schema: DatabaseSchema;
  private llmClient: any; // سيتم حقن LLM client

  constructor(schema: DatabaseSchema, llmClient: any) {
    this.schema = schema;
    this.llmClient = llmClient;
  }

  /**
   * تحليل سؤال المستخدم وتوليد SQL ذكي
   */
  async generateSQL(userQuestion: string): Promise<SQLResult> {
    console.log('🧠 بدء التحليل الذكي للسؤال:', userQuestion);

    // المرحلة 1: تحليل لغوي عميق للسؤال
    const linguisticAnalysis = await this.analyzeLinguistically(userQuestion);
    
    // المرحلة 2: تحديد الكيانات والمفاهيم
    const entities = await this.extractEntities(userQuestion, linguisticAnalysis);
    
    // المرحلة 3: ربط الكيانات بالجداول والأعمدة
    const tableMapping = await this.mapEntitiesToSchema(entities);
    
    // المرحلة 4: بناء استراتيجية الاستعلام
    const queryStrategy = await this.buildQueryStrategy(userQuestion, tableMapping);
    
    // المرحلة 5: توليد SQL النهائي
    const sqlQuery = await this.generateSQLQuery(queryStrategy);
    
    // المرحلة 6: التحقق والتحسين
    const optimizedQuery = await this.optimizeQuery(sqlQuery);

    return {
      query: optimizedQuery.sql,
      explanation: optimizedQuery.explanation,
      confidence: optimizedQuery.confidence,
      reasoning: optimizedQuery.reasoning
    };
  }

  /**
   * تحليل لغوي عميق للسؤال
   */
  private async analyzeLinguistically(question: string): Promise<any> {
    const prompt = `
🧠 **مهمة: تحليل لغوي عميق**

**السؤال:** "${question}"

قم بتحليل السؤال لغوياً وحدد:

1. **نوع السؤال:**
   - استعلام بيانات (SELECT)
   - إحصائيات (COUNT, SUM, AVG)
   - ترتيب (ORDER BY)
   - تصفية (WHERE)
   - تجميع (GROUP BY)

2. **الكيانات المذكورة:**
   - أسماء منتجات/أشخاص/أماكن
   - تواريخ وفترات زمنية
   - أرقام وكميات
   - صفات ومعايير

3. **العلاقات المطلوبة:**
   - ما الجداول التي قد تحتاج ربط؟
   - ما نوع العلاقة المطلوبة؟

4. **المخرجات المطلوبة:**
   - ما البيانات التي يريد المستخدم رؤيتها؟

أرجع النتيجة في JSON:
{
  "questionType": "...",
  "entities": [...],
  "relationships": [...],
  "expectedOutput": "..."
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * استخراج الكيانات من السؤال
   */
  private async extractEntities(question: string, analysis: any): Promise<any> {
    const prompt = `
🎯 **مهمة: استخراج الكيانات**

**السؤال:** "${question}"
**التحليل الأولي:** ${JSON.stringify(analysis)}

استخرج الكيانات التالية:

1. **منتجات/عناصر:** أي أسماء منتجات أو عناصر مذكورة
2. **أشخاص/عملاء:** أي أسماء أشخاص أو عملاء
3. **تواريخ:** أي تواريخ أو فترات زمنية (أشهر، سنوات، أيام)
4. **كميات/أرقام:** أي أرقام أو كميات مذكورة
5. **معايير:** أي شروط أو معايير للتصفية
6. **إجراءات:** ما نوع العملية المطلوبة (مجموع، عدد، متوسط، إلخ)

أرجع النتيجة في JSON:
{
  "products": [...],
  "people": [...],
  "dates": [...],
  "quantities": [...],
  "criteria": [...],
  "operations": [...]
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * ربط الكيانات بالجداول والأعمدة
   */
  private async mapEntitiesToSchema(entities: any): Promise<any> {
    const schemaDescription = this.generateSchemaDescription();
    
    const prompt = `
🔗 **مهمة: ربط الكيانات بقاعدة البيانات**

**الكيانات المستخرجة:** ${JSON.stringify(entities)}

**بنية قاعدة البيانات:**
${schemaDescription}

حدد لكل كيان:
1. **الجدول المناسب:** أي جدول يحتوي على هذا الكيان؟
2. **العمود المناسب:** أي عمود في الجدول؟
3. **نوع المطابقة:** مطابقة تامة أم جزئية (LIKE)؟
4. **الجداول المرتبطة:** أي جداول أخرى نحتاج ربطها؟

أرجع النتيجة في JSON:
{
  "tableMappings": [
    {
      "entity": "...",
      "table": "...",
      "column": "...",
      "matchType": "exact|partial",
      "relatedTables": [...]
    }
  ],
  "requiredJoins": [...]
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * بناء استراتيجية الاستعلام
   */
  private async buildQueryStrategy(question: string, mapping: any): Promise<any> {
    const prompt = `
📋 **مهمة: بناء استراتيجية الاستعلام**

**السؤال الأصلي:** "${question}"
**ربط الكيانات:** ${JSON.stringify(mapping)}

صمم استراتيجية الاستعلام:

1. **الجداول الأساسية:** ما الجداول التي نحتاجها؟
2. **نوع الـ JOINs:** كيف نربط الجداول؟
3. **شروط WHERE:** ما الشروط المطلوبة؟
4. **التجميع GROUP BY:** هل نحتاج تجميع؟
5. **الترتيب ORDER BY:** كيف نرتب النتائج؟
6. **الحدود LIMIT:** هل نحدد عدد النتائج؟

أرجع النتيجة في JSON:
{
  "mainTables": [...],
  "joins": [...],
  "whereConditions": [...],
  "groupBy": [...],
  "orderBy": [...],
  "limit": null
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * توليد SQL النهائي
   */
  private async generateSQLQuery(strategy: any): Promise<any> {
    const prompt = `
⚡ **مهمة: توليد SQL النهائي**

**استراتيجية الاستعلام:** ${JSON.stringify(strategy)}

اكتب استعلام SQL كامل وصحيح بناءً على الاستراتيجية:

1. استخدم أسماء الجداول والأعمدة الصحيحة
2. اكتب JOINs صحيحة
3. استخدم شروط WHERE مناسبة
4. طبق GROUP BY و ORDER BY حسب الحاجة
5. تأكد من صحة الـ syntax

أرجع النتيجة في JSON:
{
  "sql": "...",
  "explanation": "...",
  "steps": [...]
}
`;

    const response = await this.llmClient.generateContent(prompt);
    return this.parseJSONResponse(response);
  }

  /**
   * تحسين الاستعلام
   */
  private async optimizeQuery(query: any): Promise<any> {
    // تحسينات إضافية وتحقق من الصحة
    return {
      sql: query.sql,
      explanation: query.explanation,
      confidence: 0.9,
      reasoning: query.steps || []
    };
  }

  /**
   * توليد وصف مفصل لبنية قاعدة البيانات
   */
  private generateSchemaDescription(): string {
    let description = "📊 **بنية قاعدة البيانات:**\n\n";
    
    for (const table of this.schema.tables) {
      description += `**جدول: ${table.name}**\n`;
      description += `الوصف: ${table.description}\n`;
      description += `الأعمدة:\n`;
      
      for (const column of table.columns) {
        description += `  - ${column.name} (${column.type}): ${column.description}\n`;
      }
      
      if (table.relationships.length > 0) {
        description += `العلاقات:\n`;
        for (const rel of table.relationships) {
          description += `  - ${rel.fromColumn} → ${rel.toTable}.${rel.toColumn} (${rel.type})\n`;
        }
      }
      
      description += "\n";
    }
    
    return description;
  }

  /**
   * تحليل استجابة JSON من LLM مع التعامل مع التنسيقات المختلفة
   */
  private parseJSONResponse(response: string): any {
    try {
      // محاولة تحليل مباشر
      return JSON.parse(response);
    } catch (error) {
      // إزالة markdown code blocks
      let cleanResponse = response.trim();

      // إزالة ```json و ```
      cleanResponse = cleanResponse.replace(/^```json\s*/i, '');
      cleanResponse = cleanResponse.replace(/^```\s*/i, '');
      cleanResponse = cleanResponse.replace(/\s*```$/i, '');

      // إزالة أي نص قبل أو بعد JSON
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }

      try {
        return JSON.parse(cleanResponse);
      } catch (secondError) {
        console.error('فشل في تحليل JSON:', response);
        console.error('النص المنظف:', cleanResponse);
        throw new Error(`فشل في تحليل استجابة JSON: ${secondError}`);
      }
    }
  }
}
