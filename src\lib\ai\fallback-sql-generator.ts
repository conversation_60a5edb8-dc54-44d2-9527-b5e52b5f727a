// نظام fallback لتوليد استعلامات SQL أساسية بدون API

export interface FallbackSQLResult {
  query: string;
  explanation: string;
  confidence: number;
  isFallback: boolean;
}

// قواعد أساسية لتوليد استعلامات SQL
const BASIC_PATTERNS = [
  {
    keywords: ['مبيعات', 'منتج', 'برتقال', 'تفاح', 'موز'],
    template: `SELECT
  i.invoice_date,
  it.item_name,
  id.quantity,
  id.unit_price,
  (id.quantity * id.unit_price) AS total_amount
FROM invoices i
JOIN invoice_details id ON i.invoice_id = id.invoice_id
JOIN items it ON id.item_id = it.item_id
WHERE it.item_name LIKE '%{PRODUCT_NAME}%'
ORDER BY i.invoice_date DESC;`,
    explanation: 'استعلام لعرض مبيعات منتج معين',
    defaultLimit: null
  },
  {
    keywords: ['أكثر', 'العملاء', 'شراء', 'مشتريات'],
    template: `SELECT
  c.customer_name,
  SUM(id.quantity * id.unit_price) AS total_purchases
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
GROUP BY c.customer_id, c.customer_name
ORDER BY total_purchases DESC
LIMIT {LIMIT};`,
    explanation: 'استعلام لعرض أكثر العملاء شراءً حسب إجمالي المبلغ المشترى',
    defaultLimit: '10'
  },
  {
    keywords: ['مشتريات', 'عميل'],
    template: `SELECT 
  i.invoice_date,
  it.item_name,
  id.quantity,
  id.unit_price,
  (id.quantity * id.unit_price) AS subtotal
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
JOIN items it ON id.item_id = it.item_id
WHERE c.customer_name LIKE '%{CUSTOMER_NAME}%'
ORDER BY i.invoice_date DESC;`,
    explanation: 'استعلام لعرض مشتريات عميل معين',
    defaultLimit: null
  },
  {
    keywords: ['أكثر', 'المنتجات', 'مبيعاً', 'بيع'],
    template: `SELECT 
  it.item_name,
  SUM(id.quantity) AS total_sold,
  SUM(id.quantity * id.unit_price) AS total_revenue
FROM items it
JOIN invoice_details id ON it.item_id = id.item_id
GROUP BY it.item_id, it.item_name
ORDER BY total_sold DESC
LIMIT {LIMIT};`,
    explanation: 'استعلام لعرض أكثر المنتجات مبيعاً',
    defaultLimit: '10'
  },
  {
    keywords: ['إجمالي', 'مبيعات', 'مجموع'],
    template: `SELECT 
  COUNT(DISTINCT i.invoice_id) AS total_invoices,
  SUM(id.quantity * id.unit_price) AS total_sales,
  AVG(id.quantity * id.unit_price) AS average_sale
FROM invoices i
JOIN invoice_details id ON i.invoice_id = id.invoice_id;`,
    explanation: 'استعلام لعرض إجمالي المبيعات',
    defaultLimit: null
  }
];

// دالة لاستخراج المعاملات من النص
function extractQueryParameters(userQuestion: string): Record<string, string> {
  const params: Record<string, string> = {};

  // استخراج الأرقام
  const numberMatch = userQuestion.match(/(\d+)/);
  if (numberMatch) {
    params.LIMIT = numberMatch[1];
  }

  // استخراج أسماء العملاء المحتملة
  const customerNames = ['فاطمة علي', 'أحمد محمد', 'سارة أحمد', 'محمد علي', 'نور الدين', 'فاطمة', 'أحمد', 'سارة', 'محمد', 'نور'];
  for (const name of customerNames) {
    if (userQuestion.includes(name)) {
      params.CUSTOMER_NAME = name;
      break;
    }
  }

  // استخراج أسماء المنتجات
  const productNames = [
    'برتقال', 'تفاح', 'موز', 'عنب', 'فراولة', 'مانجو', 'أناناس',
    'خيار', 'طماطم', 'بصل', 'جزر', 'خس', 'بقدونس', 'نعناع',
    'أرز', 'سكر', 'ملح', 'زيت', 'دقيق', 'شاي', 'قهوة',
    'لحم', 'دجاج', 'سمك', 'بيض', 'حليب', 'جبن', 'زبدة'
  ];

  for (const product of productNames) {
    if (userQuestion.includes(product)) {
      params.PRODUCT_NAME = product;
      break;
    }
  }

  // إذا لم نجد منتج محدد، نحاول استخراج كلمة بعد "مبيعات"
  if (!params.PRODUCT_NAME) {
    const salesMatch = userQuestion.match(/مبيعات\s+(\w+)/);
    if (salesMatch) {
      params.PRODUCT_NAME = salesMatch[1];
    }
  }

  return params;
}

// دالة لحساب درجة التطابق
function calculateMatchScore(userQuestion: string, pattern: any): number {
  const questionLower = userQuestion.toLowerCase();
  let score = 0;
  
  for (const keyword of pattern.keywords) {
    if (questionLower.includes(keyword.toLowerCase())) {
      score += 1;
    }
  }
  
  return score;
}

// دالة لتطبيق المعاملات على القالب
function applyParameters(template: string, params: Record<string, string>, defaultLimit?: string): string {
  let query = template;
  
  // تطبيق المعاملات
  for (const [key, value] of Object.entries(params)) {
    const placeholder = `{${key}}`;
    query = query.replace(new RegExp(placeholder, 'g'), value);
  }
  
  // تطبيق القيم الافتراضية
  if (defaultLimit && query.includes('{LIMIT}')) {
    query = query.replace(/{LIMIT}/g, params.LIMIT || defaultLimit);
  }
  
  // إزالة أي placeholders متبقية
  query = query.replace(/{[^}]+}/g, '');
  
  return query;
}

// الدالة الرئيسية لتوليد SQL fallback
export function generateFallbackSQL(userQuestion: string): FallbackSQLResult | null {
  console.log('محاولة توليد استعلام SQL باستخدام نظام fallback...');
  
  let bestMatch = null;
  let bestScore = 0;
  
  // البحث عن أفضل تطابق
  for (const pattern of BASIC_PATTERNS) {
    const score = calculateMatchScore(userQuestion, pattern);
    if (score > bestScore) {
      bestScore = score;
      bestMatch = pattern;
    }
  }
  
  // إذا لم نجد تطابق جيد
  if (!bestMatch || bestScore < 2) {
    console.log('لم يتم العثور على نمط مناسب في نظام fallback');
    return null;
  }
  
  // استخراج المعاملات
  const params = extractQueryParameters(userQuestion);
  
  // تطبيق المعاملات على القالب
  const query = applyParameters(bestMatch.template, params, bestMatch.defaultLimit);
  
  console.log('تم توليد استعلام fallback بنجاح');
  
  return {
    query,
    explanation: `${bestMatch.explanation}\n\n⚠️ تم توليد هذا الاستعلام باستخدام نظام fallback بسبب عدم توفر API.`,
    confidence: Math.min(0.7, bestScore * 0.2), // ثقة أقل للـ fallback
    isFallback: true
  };
}

// دالة للتحقق من صحة الاستعلام الأساسية
export function validateBasicSQL(query: string): boolean {
  // تحققات أساسية
  const hasSelect = query.toLowerCase().includes('select');
  const hasFrom = query.toLowerCase().includes('from');
  const hasValidTables = query.includes('customers') || query.includes('invoices') || query.includes('items');
  
  return hasSelect && hasFrom && hasValidTables;
}

// دالة لتنظيف الاستعلام
export function cleanupQuery(query: string): string {
  return query
    .replace(/\s+/g, ' ') // إزالة المسافات الزائدة
    .replace(/;\s*$/, ';') // التأكد من وجود فاصلة منقوطة في النهاية
    .trim();
}
