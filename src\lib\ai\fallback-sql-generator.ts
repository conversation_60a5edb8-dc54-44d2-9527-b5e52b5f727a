// نظام fallback ذكي وعام لتوليد استعلامات SQL

export interface FallbackSQLResult {
  query: string;
  explanation: string;
  confidence: number;
  isFallback: boolean;
}

// أنماط ذكية عامة تعمل مع أي قاعدة بيانات
interface SmartPattern {
  intent: string;
  keywords: string[];
  generateQuery: (tables: any[], userQuestion: string) => string | null;
  explanation: string;
}

const SMART_PATTERNS: SmartPattern[] = [
  {
    intent: 'search_by_name',
    keywords: ['مبيعات', 'بحث', 'عن'],
    generateQuery: (tables, userQuestion) => {
      // البحث عن جداول تحتوي على name أو اسم
      const nameTable = tables.find(t =>
        t.columns.some((col: any) =>
          col.name.toLowerCase().includes('name') ||
          col.name.includes('اسم') ||
          col.name.includes('item') ||
          col.name.includes('product')
        )
      );

      if (!nameTable) return null;

      const nameColumn = nameTable.columns.find((col: any) =>
        col.name.toLowerCase().includes('name') ||
        col.name.includes('اسم') ||
        col.name.includes('item') ||
        col.name.includes('product')
      );

      // استخراج كلمة البحث
      const searchTerm = extractSearchTerm(userQuestion);
      if (!searchTerm) return null;

      return `SELECT * FROM ${nameTable.name} WHERE ${nameColumn.name} LIKE '%${searchTerm}%' ORDER BY ${nameColumn.name};`;
    },
    explanation: 'بحث ذكي في الجداول بناءً على الاسم'
  },
  {
    intent: 'top_records',
    keywords: ['أكثر', 'أعلى', 'أفضل', 'ترتيب'],
    generateQuery: (tables, userQuestion) => {
      // البحث عن جداول تحتوي على أعمدة رقمية للترتيب
      const numericTable = tables.find(t =>
        t.columns.some((col: any) =>
          col.type.includes('int') ||
          col.type.includes('decimal') ||
          col.type.includes('float') ||
          col.type.includes('number')
        )
      );

      if (!numericTable) return null;

      const numericColumn = numericTable.columns.find((col: any) =>
        col.type.includes('int') ||
        col.type.includes('decimal') ||
        col.type.includes('float')
      );

      const nameColumn = numericTable.columns.find((col: any) =>
        col.name.toLowerCase().includes('name') ||
        col.name.includes('اسم')
      ) || numericTable.columns[0];

      return `SELECT ${nameColumn.name}, ${numericColumn.name} FROM ${numericTable.name} ORDER BY ${numericColumn.name} DESC LIMIT 10;`;
    },
    explanation: 'عرض أعلى السجلات بناءً على القيم الرقمية'
  },
  {
    intent: 'count_records',
    keywords: ['عدد', 'كم', 'إحصائية', 'مجموع'],
    generateQuery: (tables, userQuestion) => {
      // اختيار أول جدول متاح
      const table = tables[0];
      if (!table) return null;

      return `SELECT COUNT(*) as total_count FROM ${table.name};`;
    },
    explanation: 'عد إجمالي السجلات في الجدول'
  }
  {
    keywords: ['أكثر', 'العملاء', 'شراء', 'مشتريات'],
    template: `SELECT
  c.customer_name,
  SUM(id.quantity * id.unit_price) AS total_purchases
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
GROUP BY c.customer_id, c.customer_name
ORDER BY total_purchases DESC
LIMIT {LIMIT};`,
    explanation: 'استعلام لعرض أكثر العملاء شراءً حسب إجمالي المبلغ المشترى',
    defaultLimit: '10'
  },
  {
    keywords: ['مشتريات', 'عميل'],
    template: `SELECT 
  i.invoice_date,
  it.item_name,
  id.quantity,
  id.unit_price,
  (id.quantity * id.unit_price) AS subtotal
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
JOIN items it ON id.item_id = it.item_id
WHERE c.customer_name LIKE '%{CUSTOMER_NAME}%'
ORDER BY i.invoice_date DESC;`,
    explanation: 'استعلام لعرض مشتريات عميل معين',
    defaultLimit: null
  },
  {
    keywords: ['أكثر', 'المنتجات', 'مبيعاً', 'بيع'],
    template: `SELECT 
  it.item_name,
  SUM(id.quantity) AS total_sold,
  SUM(id.quantity * id.unit_price) AS total_revenue
FROM items it
JOIN invoice_details id ON it.item_id = id.item_id
GROUP BY it.item_id, it.item_name
ORDER BY total_sold DESC
LIMIT {LIMIT};`,
    explanation: 'استعلام لعرض أكثر المنتجات مبيعاً',
    defaultLimit: '10'
  },
  {
    keywords: ['إجمالي', 'مبيعات', 'مجموع'],
    template: `SELECT 
  COUNT(DISTINCT i.invoice_id) AS total_invoices,
  SUM(id.quantity * id.unit_price) AS total_sales,
  AVG(id.quantity * id.unit_price) AS average_sale
FROM invoices i
JOIN invoice_details id ON i.invoice_id = id.invoice_id;`,
    explanation: 'استعلام لعرض إجمالي المبيعات',
    defaultLimit: null
  }
];

// دالة لاستخراج المعاملات من النص
function extractQueryParameters(userQuestion: string): Record<string, string> {
  const params: Record<string, string> = {};

  // استخراج الأرقام
  const numberMatch = userQuestion.match(/(\d+)/);
  if (numberMatch) {
    params.LIMIT = numberMatch[1];
  }

  // استخراج أسماء العملاء المحتملة
  const customerNames = ['فاطمة علي', 'أحمد محمد', 'سارة أحمد', 'محمد علي', 'نور الدين', 'فاطمة', 'أحمد', 'سارة', 'محمد', 'نور'];
  for (const name of customerNames) {
    if (userQuestion.includes(name)) {
      params.CUSTOMER_NAME = name;
      break;
    }
  }

  // استخراج أسماء المنتجات
  const productNames = [
    'برتقال', 'تفاح', 'موز', 'عنب', 'فراولة', 'مانجو', 'أناناس',
    'خيار', 'طماطم', 'بصل', 'جزر', 'خس', 'بقدونس', 'نعناع',
    'أرز', 'سكر', 'ملح', 'زيت', 'دقيق', 'شاي', 'قهوة',
    'لحم', 'دجاج', 'سمك', 'بيض', 'حليب', 'جبن', 'زبدة'
  ];

  for (const product of productNames) {
    if (userQuestion.includes(product)) {
      params.PRODUCT_NAME = product;
      break;
    }
  }

  // إذا لم نجد منتج محدد، نحاول استخراج كلمة بعد "مبيعات"
  if (!params.PRODUCT_NAME) {
    const salesMatch = userQuestion.match(/مبيعات\s+(\w+)/);
    if (salesMatch) {
      params.PRODUCT_NAME = salesMatch[1];
    }
  }

  return params;
}

// دالة لحساب درجة التطابق
function calculateMatchScore(userQuestion: string, pattern: any): number {
  const questionLower = userQuestion.toLowerCase();
  let score = 0;
  
  for (const keyword of pattern.keywords) {
    if (questionLower.includes(keyword.toLowerCase())) {
      score += 1;
    }
  }
  
  return score;
}

// دالة لتطبيق المعاملات على القالب
function applyParameters(template: string, params: Record<string, string>, defaultLimit?: string): string {
  let query = template;
  
  // تطبيق المعاملات
  for (const [key, value] of Object.entries(params)) {
    const placeholder = `{${key}}`;
    query = query.replace(new RegExp(placeholder, 'g'), value);
  }
  
  // تطبيق القيم الافتراضية
  if (defaultLimit && query.includes('{LIMIT}')) {
    query = query.replace(/{LIMIT}/g, params.LIMIT || defaultLimit);
  }
  
  // إزالة أي placeholders متبقية
  query = query.replace(/{[^}]+}/g, '');
  
  return query;
}

// دالة لاستخراج كلمة البحث من السؤال
function extractSearchTerm(userQuestion: string): string | null {
  // البحث عن كلمة بعد "مبيعات" أو "بحث عن"
  const patterns = [
    /مبيعات\s+(\w+)/,
    /بحث\s+عن\s+(\w+)/,
    /عرض\s+(\w+)/,
    /أين\s+(\w+)/
  ];

  for (const pattern of patterns) {
    const match = userQuestion.match(pattern);
    if (match) return match[1];
  }

  return null;
}

// الدالة الرئيسية لتوليد SQL fallback ذكي
export function generateFallbackSQL(userQuestion: string, availableTables?: any[]): FallbackSQLResult | null {
  console.log('محاولة توليد استعلام SQL باستخدام نظام fallback ذكي...');

  if (!availableTables || availableTables.length === 0) {
    console.log('لا توجد جداول متاحة للتحليل');
    return null;
  }

  // البحث عن أفضل نمط ذكي
  for (const pattern of SMART_PATTERNS) {
    const hasKeywords = pattern.keywords.some(keyword =>
      userQuestion.toLowerCase().includes(keyword)
    );

    if (hasKeywords) {
      const query = pattern.generateQuery(availableTables, userQuestion);
      if (query) {
        console.log('تم توليد استعلام fallback ذكي بنجاح');
        return {
          query: cleanupQuery(query),
          explanation: `${pattern.explanation}\n\n⚠️ تم توليد هذا الاستعلام باستخدام نظام fallback ذكي.`,
          confidence: 0.6,
          isFallback: true
        };
      }
    }
  }

  console.log('لم يتم العثور على نمط مناسب في نظام fallback');
  return null;
}

// دالة للتحقق من صحة الاستعلام الأساسية
export function validateBasicSQL(query: string): boolean {
  // تحققات أساسية
  const hasSelect = query.toLowerCase().includes('select');
  const hasFrom = query.toLowerCase().includes('from');
  const hasValidTables = query.includes('customers') || query.includes('invoices') || query.includes('items');
  
  return hasSelect && hasFrom && hasValidTables;
}

// دالة لتنظيف الاستعلام
export function cleanupQuery(query: string): string {
  return query
    .replace(/\s+/g, ' ') // إزالة المسافات الزائدة
    .replace(/;\s*$/, ';') // التأكد من وجود فاصلة منقوطة في النهاية
    .trim();
}
